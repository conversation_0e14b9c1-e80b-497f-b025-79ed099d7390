"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../lib/auth-context";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  Search,
  Globe,
  Check,
  X,
  Loader2,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { apiGet } from "../lib/api";

interface TokenRecord {
  name: string;
  type: "Chat API" | "Claude Code";
  urlPath?: string;
  apiPath?: string;
  token?: string;
  modelList?: string[];
  note?: string;
  postUrl?: string;
  status?: "active" | "failed" | "not tested" | "test again";
  canFetch?: boolean;
}

interface ModelGroup {
  model: string;
  records: TokenRecord[];
}

interface TestResult {
  success: boolean;
  response: string;
  error?: string;
  firstByteTime?: number;
  totalTime?: number;
}

export default function AggregateTestPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  const [tokens, setTokens] = useState<TokenRecord[]>([]);
  const [modelGroups, setModelGroups] = useState<ModelGroup[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedRecords, setSelectedRecords] = useState<string[]>([]);
  const [isTestLoading, setIsTestLoading] = useState(false);
  const [testResults, setTestResults] = useState<{
    [recordName: string]: TestResult;
  }>({});
  const [currentTestingRecord, setCurrentTestingRecord] = useState<string>("");
  const [expandedModel, setExpandedModel] = useState<string>("");

  // Fetch tokens
  useEffect(() => {
    if (user) {
      fetchTokens();
    }
  }, [user]);

  // Group tokens by model
  useEffect(() => {
    if (!Array.isArray(tokens)) {
      setModelGroups([]);
      return;
    }

    const groups: { [model: string]: TokenRecord[] } = {};

    tokens.forEach((token) => {
      if (token.modelList && Array.isArray(token.modelList)) {
        token.modelList.forEach((model) => {
          if (!groups[model]) {
            groups[model] = [];
          }
          groups[model].push(token);
        });
      }
    });

    const modelGroupsArray = Object.entries(groups)
      .map(([model, records]) => ({ model, records }))
      .sort((a, b) => a.model.localeCompare(b.model));

    setModelGroups(modelGroupsArray);
  }, [tokens]);

  const fetchTokens = async () => {
    try {
      const data = await apiGet("/api/tokens");
      setTokens(data);
    } catch (error) {
      console.error("Failed to fetch tokens:", error);
    }
  };

  const filteredModelGroups = modelGroups.filter((group) =>
    group.model.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleRecordSelection = (recordName: string) => {
    setSelectedRecords((prev) =>
      prev.includes(recordName)
        ? prev.filter((name) => name !== recordName)
        : [...prev, recordName]
    );
  };

  const selectAllRecordsForModel = (model: string) => {
    const group = modelGroups.find((g) => g.model === model);
    if (group) {
      const recordNames = group.records.map((r) => r.name);
      setSelectedRecords((prev) => {
        const newSelection = [...prev];
        recordNames.forEach((name) => {
          if (!newSelection.includes(name)) {
            newSelection.push(name);
          }
        });
        return newSelection;
      });
    }
  };

  const handleModelSelect = (model: string) => {
    setSelectedModel(model);
    setExpandedModel(model);
    setSelectedRecords([]);
    setTestResults({});
  };

  const runAggregateTest = async () => {
    if (!selectedModel || selectedRecords.length === 0) {
      return;
    }

    setIsTestLoading(true);
    setTestResults({});

    const selectedTokens = tokens.filter((token) =>
      selectedRecords.includes(token.name)
    );

    for (const token of selectedTokens) {
      setCurrentTestingRecord(token.name);

      const startTime = performance.now();
      let firstByteTime: number | undefined;
      let totalTime: number | undefined;

      try {
        // Construct the API URL based on token configuration
        let apiUrl = "/api/chat"; // Default fallback

        if (token.postUrl) {
          apiUrl = token.postUrl;
        } else if (token.urlPath) {
          const baseUrl = token.urlPath.replace(/\/+$/, "");
          apiUrl = `${baseUrl}/v1/chat/completions`;
        } else if (token.apiPath) {
          apiUrl = token.apiPath;
        }

        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        if (token.token) {
          headers["Authorization"] = `Bearer ${token.token}`;
        }

        const requestBody = {
          model: selectedModel,
          messages: [{ role: "user", content: "你好" }],
        };

        const response = await fetch(apiUrl, {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody),
        });

        firstByteTime = performance.now() - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          totalTime = performance.now() - startTime;
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText };
          }
          throw new Error(
            errorData.error || `HTTP ${response.status}: ${response.statusText}`
          );
        }

        const data = await response.json();
        totalTime = performance.now() - startTime;

        setTestResults((prev) => ({
          ...prev,
          [token.name]: {
            success: true,
            response: JSON.stringify(data, null, 2),
            firstByteTime,
            totalTime,
          },
        }));
      } catch (err) {
        totalTime = performance.now() - startTime;
        setTestResults((prev) => ({
          ...prev,
          [token.name]: {
            success: false,
            response: "",
            error: err instanceof Error ? err.message : "Test failed",
            firstByteTime,
            totalTime,
          },
        }));
      }
    }

    setCurrentTestingRecord("");
    setIsTestLoading(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push("/");
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          <h1 className="text-3xl font-bold">Aggregate Model Testing</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - Model Selection */}
          <div className="space-y-6">
            {/* Search */}
            <Card>
              <CardHeader>
                <CardTitle>Search Models</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search models..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Model Groups */}
            <Card>
              <CardHeader>
                <CardTitle>
                  Available Models ({filteredModelGroups.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="max-h-96 overflow-y-auto">
                <div className="space-y-2">
                  {filteredModelGroups.map((group) => (
                    <div
                      key={group.model}
                      className="border rounded-lg"
                    >
                      <div
                        className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 ${
                          selectedModel === group.model
                            ? "bg-blue-50 border-blue-200"
                            : ""
                        }`}
                        onClick={() => handleModelSelect(group.model)}
                      >
                        <div className="flex items-center gap-3">
                          {expandedModel === group.model ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                          <span className="font-medium">{group.model}</span>
                          <Badge variant="secondary">
                            {group.records.length} records
                          </Badge>
                        </div>
                        {selectedModel === group.model && (
                          <Check className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Record Selection & Testing */}
          <div className="space-y-6">
            {selectedModel && (
              <>
                {/* Record Selection */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Records with {selectedModel}</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectAllRecordsForModel(selectedModel)}
                      >
                        Select All
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {modelGroups
                        .find((g) => g.model === selectedModel)
                        ?.records.map((record) => (
                          <div
                            key={record.name}
                            className={`flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50 ${
                              selectedRecords.includes(record.name)
                                ? "bg-blue-50 border-blue-200"
                                : ""
                            }`}
                            onClick={() => toggleRecordSelection(record.name)}
                          >
                            <div>
                              <div className="font-medium">{record.name}</div>
                              <div className="text-sm text-gray-500">
                                {record.type}
                              </div>
                            </div>
                            {selectedRecords.includes(record.name) && (
                              <Check className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Test Button */}
                {selectedRecords.length > 0 && (
                  <Card>
                    <CardContent className="pt-6">
                      <Button
                        onClick={runAggregateTest}
                        disabled={isTestLoading}
                        className="w-full"
                        size="lg"
                      >
                        {isTestLoading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            {currentTestingRecord
                              ? `Testing ${currentTestingRecord}...`
                              : "Testing..."}
                          </>
                        ) : (
                          <>
                            <Globe className="w-4 h-4 mr-2" />
                            Test {selectedRecords.length} Records with{" "}
                            {selectedModel}
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Test Results for {selectedModel}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(testResults).map(([recordName, result]) => (
                  <div
                    key={recordName}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{recordName}</h4>
                      <Badge
                        variant={result.success ? "default" : "destructive"}
                      >
                        {result.success ? "Success" : "Failed"}
                      </Badge>
                    </div>

                    {/* Response Time Information */}
                    {(result.firstByteTime !== undefined ||
                      result.totalTime !== undefined) && (
                      <div className="bg-gray-50 p-3 rounded mb-3 grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="font-medium text-gray-700">
                            First Byte Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.firstByteTime !== undefined
                              ? `${result.firstByteTime.toFixed(0)}ms`
                              : "N/A"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium text-gray-700">
                            Total Response Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.totalTime !== undefined
                              ? `${result.totalTime.toFixed(0)}ms`
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    )}

                    {result.success ? (
                      <Textarea
                        value={result.response}
                        readOnly
                        className="min-h-[150px] font-mono text-sm text-green-600 border-green-300"
                      />
                    ) : (
                      <div className="bg-red-50 p-3 rounded border border-red-200">
                        <p className="text-red-600 text-sm font-mono">
                          {result.error}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
