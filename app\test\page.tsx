"use client";

import { useState, useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useAuth } from "../lib/auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Send, Loader2 } from "lucide-react";
import { apiGet } from "../lib/api";

interface TokenRecord {
  name: string;
  type: "Chat API" | "Claude Code";
  urlPath?: string;
  apiPath?: string;
  token?: string;
  modelList?: string[];
  note?: string;
  postUrl?: string;
  status?: "active" | "failed" | "not tested" | "test again";
  canFetch?: boolean;
}

export default function TestPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const tokenName = searchParams.get("token");

  const [token, setToken] = useState<TokenRecord | null>(null);
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<string>("");
  const [error, setError] = useState<string>("");

  // Fetch token details
  useEffect(() => {
    if (user && tokenName) {
      fetchTokenDetails();
    }
  }, [user, tokenName]);

  const fetchTokenDetails = async () => {
    try {
      const tokens = await apiGet("/api/tokens");
      const foundToken = tokens.find((t: TokenRecord) => t.name === tokenName);
      if (foundToken) {
        setToken(foundToken);
        // Set first model as default if available
        if (foundToken.modelList && foundToken.modelList.length > 0) {
          setSelectedModel(foundToken.modelList[0]);
        }
      } else {
        setError("Token not found");
      }
    } catch (err) {
      setError("Failed to fetch token details");
    }
  };

  const handleTest = async () => {
    if (!token || !selectedModel) {
      setError("Please select a model");
      return;
    }

    setIsLoading(true);
    setResponse("");
    setError("");

    try {
      // Construct the API URL based on token configuration
      let apiUrl = "/api/chat"; // Default fallback

      if (token.postUrl) {
        // If postUrl is provided, use it directly
        apiUrl = token.postUrl;
      } else if (token.urlPath) {
        // If urlPath is provided, append /v1/chat/completions
        apiUrl = `${token.urlPath}/v1/chat/completions`;
      } else if (token.apiPath) {
        // If only apiPath is provided, use it as relative path
        apiUrl = token.apiPath;
      }

      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      // Add authorization header if token is provided
      if (token.token) {
        headers["Authorization"] = `Bearer ${token.token}`;
      }

      const requestBody = {
        model: selectedModel,
        messages: [{ role: "user", content: "你好" }],
      };

      const response = await fetch(apiUrl, {
        method: "POST",
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();
      setResponse(JSON.stringify(data, null, 2));
    } catch (err) {
      setError(err instanceof Error ? err.message : "Test failed");
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push("/");
    return null;
  }

  if (!token) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center">
            <p className="text-gray-600">Token not found</p>
            <Button
              onClick={() => router.push("/")}
              className="mt-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">API Test</h1>
        </div>

        {/* Token Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              {token.name}
              <Badge
                variant={token.type === "Chat API" ? "default" : "secondary"}
              >
                {token.type}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {token.urlPath && (
                <div>
                  <Label className="font-medium">URL Path:</Label>
                  <p className="text-gray-600">{token.urlPath}</p>
                </div>
              )}
              {token.apiPath && (
                <div>
                  <Label className="font-medium">API Path:</Label>
                  <p className="text-gray-600">{token.apiPath}</p>
                </div>
              )}
              {token.postUrl && (
                <div>
                  <Label className="font-medium">Post URL:</Label>
                  <p className="text-gray-600">{token.postUrl}</p>
                </div>
              )}
              {token.note && (
                <div>
                  <Label className="font-medium">Note:</Label>
                  <p className="text-gray-600">{token.note}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Model Selection */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Select Model</CardTitle>
          </CardHeader>
          <CardContent>
            {token.modelList && token.modelList.length > 0 ? (
              <div className="space-y-2">
                {token.modelList.map((model) => (
                  <label
                    key={model}
                    className="flex items-center space-x-2"
                  >
                    <input
                      type="radio"
                      name="model"
                      value={model}
                      checked={selectedModel === model}
                      onChange={(e) => setSelectedModel(e.target.value)}
                      className="text-blue-600"
                    />
                    <span>{model}</span>
                  </label>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No models available</p>
            )}
          </CardContent>
        </Card>

        {/* Test Configuration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="font-medium">API URL:</Label>
              <p className="text-gray-600 font-mono text-sm">
                {token.postUrl ||
                  (token.urlPath
                    ? `${token.urlPath}/v1/chat/completions`
                    : token.apiPath || "/api/chat")}
              </p>
            </div>
            <div>
              <Label className="font-medium">Selected Model:</Label>
              <p className="text-gray-600">
                {selectedModel || "None selected"}
              </p>
            </div>
            <div>
              <Label className="font-medium">Authorization:</Label>
              <p className="text-gray-600">
                {token.token
                  ? "Bearer token configured"
                  : "No token configured"}
              </p>
            </div>
            <div>
              <Label className="font-medium">Test Message:</Label>
              <p className="text-gray-600">"你好"</p>
            </div>
          </CardContent>
        </Card>

        {/* Test Button */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test API</CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleTest}
              disabled={isLoading || !selectedModel}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Test Message
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Response */}
        {(response || error) && (
          <Card>
            <CardHeader>
              <CardTitle className={error ? "text-red-600" : "text-green-600"}>
                {error ? "Error" : "Response"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={error || response}
                readOnly
                className={`min-h-[200px] font-mono text-sm ${
                  error ? "text-red-600" : "text-green-600"
                }`}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
