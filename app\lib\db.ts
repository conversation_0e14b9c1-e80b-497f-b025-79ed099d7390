import Database from "better-sqlite3"
import path from "path"
import fs from "fs"

const dataDir = path.join(process.cwd(), "data")
const dbPath = path.join(dataDir, "tokens.db")

// Ensure data directory exists
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}

const db = new Database(dbPath)

// Initialize database
db.exec(`
  CREATE TABLE IF NOT EXISTS tokens (
    name TEXT PRIMARY KEY,
    type TEXT NOT NULL DEFAULT 'Chat API',
    url_path TEXT,
    api_path TEXT,
    token TEXT,
    model_list TEXT,
    note TEXT,
    post_url TEXT,
    status TEXT DEFAULT 'not tested',
    can_fetch BOOLEAN DEFAULT 0,
    user_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )
`)

// Add new columns if they don't exist (for existing databases)
try {
  db.exec(`ALTER TABLE tokens ADD COLUMN status TEXT DEFAULT 'not tested'`)
} catch (e) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN can_fetch BOOLEAN DEFAULT 0`)
} catch (e) {
  // Column already exists
}

try {
  db.exec(`ALTER TABLE tokens ADD COLUMN type TEXT NOT NULL DEFAULT 'Chat API'`)
} catch (error) {
  // Column already exists
}

export interface TokenRecord {
  name: string
  type: 'Chat API' | 'Claude Code'
  urlPath?: string
  apiPath?: string
  token?: string
  modelList?: string[]
  note?: string
  postUrl?: string
  status?: 'active' | 'failed' | 'not tested' | 'test again'
  canFetch?: boolean
  userId?: string
}

export class TokenService {
  static getAllTokens(userId: string, search?: string, typeFilter?: string): TokenRecord[] {
    let query = "SELECT * FROM tokens WHERE user_id = ?"
    const params: any[] = [userId]

    if (typeFilter) {
      query += " AND type = ?"
      params.push(typeFilter)
    }

    if (search) {
      query += ` AND (
        name LIKE ? OR
        type LIKE ? OR
        url_path LIKE ? OR
        api_path LIKE ? OR
        token LIKE ? OR
        model_list LIKE ? OR
        note LIKE ? OR
        post_url LIKE ?
      )`
      const searchPattern = `%${search}%`
      params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }

    query += " ORDER BY created_at DESC"

    const stmt = db.prepare(query)
    const rows = stmt.all(...params) as any[]

    return rows.map((row) => ({
      name: row.name,
      type: row.type || 'Chat API',
      urlPath: row.url_path,
      apiPath: row.api_path,
      token: row.token,
      modelList: row.model_list ? JSON.parse(row.model_list) : [],
      note: row.note,
      postUrl: row.post_url,
      status: row.status || 'not tested',
      canFetch: Boolean(row.can_fetch),
    }))
  }

  static createToken(token: TokenRecord, userId: string): string {
    const stmt = db.prepare(`
      INSERT INTO tokens (name, type, url_path, api_path, token, model_list, note, post_url, status, can_fetch, user_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      token.name,
      token.type,
      token.urlPath || null,
      token.apiPath || null,
      token.token || null,
      token.modelList ? JSON.stringify(token.modelList) : null,
      token.note || null,
      token.postUrl || null,
      token.status || 'not tested',
      token.canFetch ? 1 : 0,
      userId,
    )

    return token.name
  }

  static updateToken(name: string, token: TokenRecord, userId: string): boolean {
    const stmt = db.prepare(`
      UPDATE tokens
      SET type = ?, url_path = ?, api_path = ?, token = ?, model_list = ?, note = ?, post_url = ?, status = ?, can_fetch = ?, updated_at = CURRENT_TIMESTAMP
      WHERE name = ? AND user_id = ?
    `)

    const result = stmt.run(
      token.type,
      token.urlPath || null,
      token.apiPath || null,
      token.token || null,
      token.modelList ? JSON.stringify(token.modelList) : null,
      token.note || null,
      token.postUrl || null,
      token.status || 'not tested',
      token.canFetch ? 1 : 0,
      name,
      userId,
    )

    return result.changes > 0
  }

  static deleteToken(name: string, userId: string): boolean {
    const stmt = db.prepare("DELETE FROM tokens WHERE name = ? AND user_id = ?")
    const result = stmt.run(name, userId)
    return result.changes > 0
  }
}

export default db
