-- Create data directory and initialize database
-- This will be automatically created when the app starts

CREATE TABLE IF NOT EXISTS tokens (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  url_path TEXT NOT NULL,
  api_path TEXT NOT NULL,
  token TEXT NOT NULL,
  model_list TEXT NOT NULL,
  note TEXT,
  user_id TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create index for better search performance
CREATE INDEX IF NOT EXISTS idx_tokens_user_id ON tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_tokens_name ON tokens(name);
