"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function SignInPage() {
  const handleLogin = () => {
    window.location.href = "/api/auth/login"
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Token Manager</CardTitle>
          <CardDescription>
            Secure token management with Keycloak authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button className="w-full" onClick={handleLogin}>
            Login with Keycloak
          </Button>
          <p className="text-sm text-gray-600 text-center">
            You will be redirected to Keycloak for secure authentication
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
